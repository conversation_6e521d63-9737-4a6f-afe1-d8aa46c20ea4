import Loader from "@/components/Loader";
import { useNavigate } from "@tanstack/react-router";
import { useFolderContext } from "@/contexts/FolderContext";
import { useEffect, useState } from "react";

export default function Pages() {
  const navigate = useNavigate();
  const { selectedFolderId, folders, isLoading: foldersLoading } = useFolderContext();
  const [hasNavigated, setHasNavigated] = useState(false);

  // Comprehensive logging for debugging
  useEffect(() => {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] Pages - Component state:`, {
      foldersLoading,
      selectedFolderId,
      foldersCount: folders?.length,
      foldersData: folders?.map(f => ({ id: f.folder_id, name: f.name })),
      shouldRedirect: !foldersLoading && folders && folders.length > 0,
      hasNavigated
    });
  });

  // Use useEffect to handle navigation to avoid "setState during render" warning
  useEffect(() => {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] Pages - Navigation useEffect triggered:`, {
      foldersLoading,
      foldersCount: folders?.length,
      selectedFolderId,
      shouldNavigate: !foldersLoading && folders && folders.length > 0,
      hasNavigated
    });

    if (!foldersLoading && folders && folders.length > 0 && !hasNavigated) {
      const targetFolder = selectedFolderId || folders[0].folder_id;
      console.log(`[${timestamp}] Pages - Navigating to folder:`, { targetFolder });
      setHasNavigated(true);
      navigate({ to: "/pages/$folderId", params: { folderId: targetFolder }, replace: true });
    }
  }, [foldersLoading, folders, selectedFolderId, navigate, hasNavigated]);

  if (foldersLoading) {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] Pages - Showing loader (folders loading)`);
    return <Loader />;
  }

  // If no folders exist, show empty state
  if (!foldersLoading && (!folders || folders.length === 0)) {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] Pages - Showing no folders state`);
    return (
      <div className="mx-4 my-4 h-full w-full">
        <div className="flex h-full w-full items-center justify-center">
          <div className="text-center">
            <span className="text-sm text-text-secondary">No folders found. Please create a folder first.</span>
          </div>
        </div>
      </div>
    );
  }

  // If we have folders and have initiated navigation, return null to let router handle the transition
  if (!foldersLoading && folders && folders.length > 0 && hasNavigated) {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] Pages - Navigation initiated, returning null`);
    return null;
  }

  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] Pages - Showing fallback loader (waiting for navigation)`);
  return <Loader />;
}
