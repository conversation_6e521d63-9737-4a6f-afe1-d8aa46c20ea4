import { useQuery } from "@tanstack/react-query";
import { getFolders, getPagesByFolder, Folder, getPages, getMostRecentlyUsedFolder } from "@/db/draw";
import { useAuth } from "./useAuth";
import React from "react";

export function useFolders() {
  const { user } = useAuth();

  const {
    data: folders,
    isLoading,
    error,
    refetch: refetchFolders,
  } = useQuery({
    queryKey: ["folders", user?.id],
    queryFn: async () => {
      const timestamp = new Date().toISOString();
      console.log(`[${timestamp}] Hook - useFolders queryFn called for user:`, user?.id);

      if (!user?.id) {
        console.log(`[${timestamp}] Hook - useFolders no user ID, returning empty array`);
        return [];
      }

      const response = await getFolders(user.id);
      console.log(`[${timestamp}] Hook - useFolders response:`, {
        dataCount: response.data?.length,
        error: response.error,
        data: response.data?.map(f => ({ id: f.folder_id, name: f.name }))
      });

      return response.data || [];
    },
    enabled: !!user?.id,
  });

  // Log the hook state
  React.useEffect(() => {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] Hook - useFolders state:`, {
      userId: user?.id,
      isLoading,
      error,
      foldersCount: folders?.length,
      folders: folders?.map(f => ({ id: f.folder_id, name: f.name }))
    });
  }, [user?.id, isLoading, error, folders]);

  return {
    folders: folders as Folder[] | undefined,
    isLoading,
    error,
    refetchFolders,
  };
}

export function useFolderPages(folderId: string | null) {
  const { user } = useAuth();

  const {
    data: pages,
    isLoading,
    error,
    refetch: refetchPages,
  } = useQuery({
    queryKey: ["folderPages", user?.id, folderId],
    queryFn: async () => {
      const timestamp = new Date().toISOString();
      console.log(`[${timestamp}] Hook - useFolderPages queryFn called:`, { userId: user?.id, folderId });

      if (!user?.id || !folderId) {
        console.log(`[${timestamp}] Hook - useFolderPages missing user or folder, returning empty array:`, { userId: user?.id, folderId });
        return [];
      }

      const response = await getPagesByFolder(user.id, folderId);
      console.log(`[${timestamp}] Hook - useFolderPages response:`, {
        userId: user?.id,
        folderId,
        dataCount: response.data?.length,
        error: response.error,
        data: response.data?.map(p => ({ id: p.page_id, name: p.name }))
      });

      return response.data || [];
    },
    enabled: !!user?.id && !!folderId,
  });

  // Log the hook state
  React.useEffect(() => {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] Hook - useFolderPages state:`, {
      userId: user?.id,
      folderId,
      isLoading,
      error,
      pagesCount: pages?.length,
      enabled: !!user?.id && !!folderId,
      pages: pages?.map(p => ({ id: p.page_id, name: p.name }))
    });
  }, [user?.id, folderId, isLoading, error, pages]);

  return {
    pages,
    isLoading,
    error,
    refetchPages,
  };
}

export function useFolderPageCounts() {
  const { user } = useAuth();

  const {
    data: pageCounts,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["folderPageCounts", user?.id],
    queryFn: async () => {
      if (!user?.id) return {};
      const response = await getPages(user.id);
      const pages = response.data || [];

      // Count pages per folder
      const counts: Record<string, number> = {};
      pages.forEach((page: any) => {
        if (page.folder_id) {
          counts[page.folder_id] = (counts[page.folder_id] || 0) + 1;
        }
      });

      return counts;
    },
    enabled: !!user?.id,
  });

  return {
    pageCounts: pageCounts || {},
    isLoading,
    error,
  };
}

export function useMostRecentlyUsedFolder() {
  const { user } = useAuth();

  const {
    data: recentFolder,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["mostRecentlyUsedFolder", user?.id],
    queryFn: async () => {
      const timestamp = new Date().toISOString();
      console.log(`[${timestamp}] Hook - useMostRecentlyUsedFolder queryFn called for user:`, user?.id);

      if (!user?.id) {
        console.log(`[${timestamp}] Hook - useMostRecentlyUsedFolder no user ID, returning null`);
        return null;
      }

      const response = await getMostRecentlyUsedFolder(user.id);
      console.log(`[${timestamp}] Hook - useMostRecentlyUsedFolder response:`, {
        error: response.error,
        data: response.data?.[0]
      });

      return response.data?.[0] || null;
    },
    enabled: !!user?.id,
  });

  // Log the hook state
  React.useEffect(() => {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] Hook - useMostRecentlyUsedFolder state:`, {
      userId: user?.id,
      isLoading,
      error,
      recentFolder
    });
  }, [user?.id, isLoading, error, recentFolder]);

  return {
    recentFolder,
    isLoading,
    error,
  };
}
